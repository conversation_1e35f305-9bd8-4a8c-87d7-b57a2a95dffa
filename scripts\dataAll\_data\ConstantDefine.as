package dataAll._data
{
   import com.sounto.oldUtils.StringDate;
   import dataAll.gift.anniver.AnniversaryDefine;
   
   public class ConstantDefine
   {
      
      public static const WIDTH:int = 950;
      
      public static const HEIGHT:int = 600;
      
      private static const discountStartTimeStr:String = "2018-1-31";
      
      private static const discountEndTimeStr:String = "2018-2-6 23:59:59";
      
      public static var nowIsDiscountB:Boolean = false;
      
      public static const versionNumber:String = "34.64";

      public static const inVersion:String = "34.6400";
      
      public static const xmlSwfUrl:String = "swf/local3464.swf";
      
      public static const loadingSwfUrl:String = "swf/UI/LoadingUI317.swf";
      
      public static const versionUrl:String = "https://my.4399.com/forums/thread-64118342";
      
      public static const testUrl:String = "open.4399.cn/";
      
      public static const versionText:String = "";
      
      public static const cnName:String = "爆枪突击";
      
      public static const playerName:String = "爆枪小战士";
      
      public static const noBadArr:Array = [playerName,""];
      
      public static const maxSkillNum:int = 10;
      
      public static const anniver:AnniversaryDefine = new AnniversaryDefine();
      
      public function ConstantDefine()
      {
         super();
      }
      
      public static function staticInit() : void
      {
         anniver.init();
      }
      
      public static function getVersionText() : String
      {
         return versionNumber + " " + versionText;
      }
      
      public static function getAllVer() : String
      {
         return "版本号：" + versionNumber + "  VER " + inVersion;
      }
      
      public static function getMainVersionNumber(num0:Number) : Number
      {
         return Number(num0.toFixed(1));
      }
      
      public static function getNowMainVersionNumber() : Number
      {
         return Number(Number(versionNumber).toFixed(1));
      }
      
      public static function getLevelPro(heroLv0:int, enemyLv0:int) : Number
      {
         var pro0:Number = 1;
         if(heroLv0 > enemyLv0 + 1)
         {
            pro0 = 1 / (0.2 + 0.8 * (heroLv0 - enemyLv0 - 1));
         }
         return pro0;
      }
      
      public static function fleshNowIsDiscountB(timeDate0:StringDate) : Boolean
      {
         var start0:StringDate = new StringDate(discountStartTimeStr);
         var end0:StringDate = new StringDate(discountEndTimeStr);
         if(start0.compareDateValue(timeDate0) >= 0 && timeDate0.compareDateValue(end0) >= 0)
         {
            nowIsDiscountB = true;
         }
         else
         {
            nowIsDiscountB = false;
         }
         return nowIsDiscountB;
      }
   }
}

